from playwright.sync_api import sync_playwright
import random
import time
import json


def run_codegen():
    with sync_playwright() as p:
        # 使用带反检测参数的浏览器
        browser = p.chromium.launch(
            headless=False,
            args=[
                '--disable-blink-features=AutomationControlled',
                '--disable-infobars',
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-web-security',
                '--disable-features=IsolateOrigins,site-per-process',
                '--disable-site-isolation-trials',
                '--disable-extensions',
                '--disable-popup-blocking',
                '--ignore-certificate-errors',
                '--disable-browser-side-navigation',
                '--disable-gpu',
                '--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/114.0.0.0 Safari/537.36',
                '--remote-debugging-port=9222'
            ],
            # 使用真实浏览器配置文件（可选）
            # user_data_dir=r'C:\Users\<USER>\AppData\Local\Google\Chrome\User Data'
        )

        # 创建带反检测脚本的上下文
        context = browser.new_context(
            bypass_csp=True,
            viewport={"width": 1366, "height": 768},
            locale="zh-CN",
            timezone_id="Asia/Shanghai",
            # 注入反检测脚本
            js="""
            // 核心反检测脚本
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined
            });

            // 伪装插件
            Object.defineProperty(navigator, 'plugins', {
                get: () => [
                    {name: 'Chrome PDF Viewer', filename: 'internal-pdf-viewer', description: 'Portable Document Format'},
                    {name: 'Chrome PDF Viewer', filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai', description: ''},
                    {name: 'Native Client', filename: 'internal-nacl-plugin', description: ''}
                ]
            });

            // 伪装语言
            Object.defineProperty(navigator, 'languages', {
                get: () => ['zh-CN', 'zh', 'en-US', 'en']
            });

            // 伪装权限
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );

            // 伪装WebGL
            const getParameter = WebGLRenderingContext.prototype.getParameter;
            WebGLRenderingContext.prototype.getParameter = function(parameter) {
                if (parameter === 37445) return 'Intel Open Source Technology Center';
                if (parameter === 37446) return 'Mesa DRI Intel(R) HD Graphics 520 (Skylake GT2)';
                return getParameter.apply(this, arguments);
            };

            // 覆盖console.debug方法
            console.debug = function() {};
            """
        )

        # 创建页面
        page = context.new_page()

        # 添加随机延迟模拟人类行为
        time.sleep(random.uniform(1.0, 3.0))

        # 访问目标网站
        page.goto(
            "https://zydz-menhu.ouchn.edu.cn/learningPlatformH5/myCourse",
            wait_until="networkidle",
            timeout=60000
        )

        # 检查是否被重定向
        if "blank" in page.url.lower() or "about:blank" in page.url:
            print("检测到重定向到空白页，尝试更多反检测措施...")
            # 尝试更彻底的反检测
            page.evaluate("""
                () => {
                    // 删除所有可能暴露自动化的属性
                    delete window._cdc_;
                    delete window.document.documentElement.dataset.webdriver;
                    delete window.navigator.__driver_evaluate;
                    delete window.navigator.__webdriver_evaluate;

                    // 覆盖window.chrome
                    window.chrome = {
                        app: {
                            isInstalled: false,
                        },
                        webstore: {
                            onInstallStageChanged: {},
                            onDownloadProgress: {},
                        },
                        runtime: {
                            PlatformOs: {
                                MAC: 'mac',
                                WIN: 'win',
                                ANDROID: 'android',
                                CROS: 'cros',
                                LINUX: 'linux',
                                OPENBSD: 'openbsd',
                            },
                            PlatformArch: {
                                ARM: 'arm',
                                X86_32: 'x86-32',
                                X86_64: 'x86-64',
                            },
                            PlatformNaclArch: {
                                ARM: 'arm',
                                X86_32: 'x86-32',
                                X86_64: 'x86-64',
                            },
                            RequestUpdateCheckStatus: {
                                THROTTLED: 'throttled',
                                NO_UPDATE: 'no_update',
                                UPDATE_AVAILABLE: 'update_available',
                            },
                            OnInstalledReason: {
                                INSTALL: 'install',
                                UPDATE: 'update',
                                CHROME_UPDATE: 'chrome_update',
                                SHARED_MODULE_UPDATE: 'shared_module_update',
                            },
                            OnRestartRequiredReason: {
                                APP_UPDATE: 'app_update',
                                OS_UPDATE: 'os_update',
                                PERIODIC: 'periodic',
                            },
                        },
                    };

                    // 伪装浏览器特征
                    Object.defineProperty(navigator, 'platform', {
                        get: () => 'Win32'
                    });

                    Object.defineProperty(navigator, 'maxTouchPoints', {
                        get: () => 0
                    });

                    Object.defineProperty(navigator, 'hardwareConcurrency', {
                        get: () => 8
                    });
                }
            """)

            # 重新加载页面
            page.reload(wait_until="networkidle", timeout=60000)

        # 检查是否成功加载
        print("当前URL:", page.url)
        print("页面标题:", page.title())

        # 保存页面内容用于调试
        with open("page_content.html", "w", encoding="utf-8") as f:
            f.write(page.content())

        # 启动录制模式
        print("启动录制模式...")
        page.pause()

        # 关闭浏览器
        browser.close()


if __name__ == "__main__":
    run_codegen()