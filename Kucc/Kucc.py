"""Welcome to Reflex! This file outlines the steps to create a basic app."""

import reflex as rx

from rxconfig import config


class State(rx.State):
    """The app state."""

class TextState(rx.State):
        text: str = ""

        @rx.event
        def update_text(self, new_text: str):
            self.text = new_text


def index() -> rx.Component:
    # Welcome Page (Index)
    return rx.container(
        rx.color_mode.button(position="top-right"),
        rx.vstack(
            rx.heading(TextState.text),
            rx.input(
                default_value=TextState.text,
                on_change=TextState.update_text,
            ),
            spacing="5",
            justify="center",
            min_height="85vh",
        ),
    )


app = rx.App()
app.add_page(index)
