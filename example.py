from playwright.sync_api import sync_playwright
from playwright.sync_api import Page, expect
import re
import time

playwright = None
browser = None
page = None

课程 = [
    "语文(上)",
    "语文(下)",
    "数学(下)",
    "沟通技巧",
    "就业指导",
    "职业健康与安全",
    "数学(上)",
    "多媒体技术基础",
    "Office办公软件",
    "办公设备使用与维护",
    "职业应用写作",
    "历史",
    "个人与团队",
    "Windows操作系统",
    "学习指南",
    "计算机职业素养",
    "计算机常用工具软件",
    "计算机网络基础",
    "中国特色社会主义",
    "职业道德与法治",
    "哲学与人生",
    "心理健康与职业生涯",
    "信息技术"
]

def init_playwright(port=6666):
    """初始化 Playwright 和浏览器连接"""
    global playwright, browser, page

    try:
        # 启动 Playwright
        playwright = sync_playwright().start()

        # 尝试连接现有浏览器
        browser = playwright.chromium.connect_over_cdp(f"http://localhost:{port}")

        # 获取页面
        context = browser.contexts[0] if browser.contexts else browser.new_context()
        page = context.pages[0] if context.pages else context.new_page()
        page.bring_to_front()

        page.pause()  # 进入录制模式

        print(f"已连接到页面: {page.url}")
        return True
    except Exception as e:
        print(f"连接失败: {e}")
        return False


def 导航到课程详情(page: Page,browser) -> None:
        # 示例：在附加后执行操作
        page.goto("https://zydz-menhu.ouchn.edu.cn/learningPlatformH5/home")
        page.get_by_text("查看全部").click()
        try:
            for i in range(1, 50):
                e = page.locator(f"div:nth-child({i}) > .module_course_info_container > .progressView > .linear_container > .linear_content > .bubble")
                t = e.inner_text()
                if t != "100%":
                    for j in 课程:
                        if page.locator(f"div:nth-child({i}) > .module_course_info_container > .majorName > div > span").inner_text() == j:
                            print(f"进入课程: {j}")
                            page.locator(f"div:nth-child({i}) > .module_course_info_container > .majorName > div > span").click()
                            return True

        except Exception as e:
            print(f"默认50，不是致命错误: {e}")


def 检测链接(page: Page):
    print(f"当前链接: {page.url}")
    return page.url



if __name__ == "__main__":
    init_playwright()
    print(page.url)
